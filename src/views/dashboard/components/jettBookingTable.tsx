import { getFile } from "apis/file.api";
import {
  getPaidJettTransactions,
  getReleasedJettTransactions,
} from "apis/payone.api";
import { FullModal } from "common/components/modals";
import { MainTable } from "common/components/tables/main.table";
import { Button } from "common/ui/button";
import { DatePickerWithRange } from "common/ui/inputs/dateRangePicker";
import { CustomSearch } from "common/ui/inputs/search";
import searchedArray from "helpers/search";
import transformDate, { convertToJordanTime } from "helpers/transformDate";
import { ChevronDown, Copy, View } from "lucide-react";
import React, { useEffect, useState } from "react";
import { DateRange } from "react-day-picker";
import toast from "react-hot-toast";

export default function JettBookingTable() {
  const [payoneJettTransactions, setPayoneJettTransactions] = useState([]);
  const [releasedJettTransactions, setReleasedJettTransactions] = useState([]);
  const [keySearch, setKeySearch] = useState("");
  const [keySearchR, setKeySearchR] = useState("");
  const [renderedTransactions, setRenderedTransactions] = useState([]);
  const [renderedReleasedTransactions, setRenderedReleasedTransactions] =
    useState([]);
  const [item, setItem] = useState(null);
  const [itemDetails, setItemDetails] = useState({});

  const [exportModel, setExportModel] = useState(false);
  const [date, setDate] = useState<DateRange | undefined>();
  const [loading, setLoading] = useState<boolean>(false);

  const [tracks, setTracks] = useState<string[]>([]);

  const [totalPassengers, setTotalPassengers] = useState<number>(0);

  const [filterType, setFilterType] = useState<string[]>([]);
  const [filterChannel, setFilterChannel] = useState<string[]>([]);
  const [filterPayment, setFilterPayment] = useState<string[]>([]);
  const [filterStatus, setFilterStatus] = useState<string[]>([]);
  const [filterDate, setFilterDate] = useState<DateRange | undefined>();
  const [filterTrack, setFilterTrack] = useState<string[]>([]);

  const [refundedPrice, setRefundedPrice] = useState<number>(0);

  useEffect(() => {
    const today = new Date();
    const lastWeek = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 7
    );
    setDate({ from: lastWeek, to: today });
  }, []);

  const getDetails = async (url) => {
    let details = {};
    const file = await getFile(url);
    if (file?.file_data) {
      details = { ...JSON.parse(file?.file_data) };
    }
    return details;
  };

  useEffect(() => {
    console.log("use effect");
    if (
      item &&
      item.type === "Booking" &&
      (item.status === "deleted" || item.status === "Released")
    ) {
      getDetails(item.collected_data_url).then((data) => {
        if (data) {
          setItemDetails({ ...data });
        }
      });
    }
  }, [item]);
  const modifyData = (data, type, item) => {
    if (type === "KHB") {
      const bookedDate = new Date(item.updatedAt).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
      const dataToDisplay = [
        {
          name: data.name,
          email: data.email,
          booked_date: bookedDate,
          trip_date: data.date,
          trip_time: data.time,
          luggage_count: data.luggageCount,
          total_price: data.totalPrice,
          transaction_id: data.transactionID,
          extra_passengers: data.extras
            ? data.extras
                .map((extra) => {
                  return extra.name;
                })
                .join(", ")
            : "",
        },
      ];
      return dataToDisplay;
    } else if (type === "cardRecharge") {
      const dataToDisplay = [
        {
          card_number: data.cardNumber,
          subscription_number: data.subscriptionNumber,
          amount: data.amount,
          transaction_id: data.transactionID,
        },
      ];
      return dataToDisplay;
    } else {
      const dataArray = Object.keys(data).map((key) => {
        return data[key];
      });
      const bookedDate = new Date(item.updatedAt).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
      const dataToDisplay = dataArray.map((reservation) => {
        return {
          name: reservation.name,
          trip_name: reservation.chosenTrack?.path,
          tel: reservation.phone,
          email: reservation.email,
          nationality: reservation.nationalID,
          tkt: reservation.reservation.reservationID,
          transaction_ID: reservation.transactionID,
          amount: reservation.amount,
          total_price: reservation.chosenTrip.totalPrice,
          booked_date: bookedDate,
          trip_date: reservation.chosenTrip.DEPARTURE_DATE,
          trip_time: reservation.chosenTrip.DEPARTURE_TIME,
          trip_seat: reservation.seat,
          payment_way: item.payment,
        };
      });
      return dataToDisplay;
    }
  };

  const tableData = () => {
    if (!item) return { data: [], columns: [] };
    if (item.type === "KHB") {
      // const dataToDisplay = modifyData(details, "KHB", item);
      const dataToDisplay = item.tickets?.map((ticket) => {
        return {
          name: ticket.name,
          email: ticket.email,
          ticket_id: ticket.ticket_id,
          price: ticket.amount,
          total_price: item.total_price,
          trip_id: ticket.trip_id,
          trip_date: ticket.trip_date,
          trip_time: ticket.trip_time,
          luggage_count: ticket.luggage_count,
          extra_adult_passengers: ticket.extra_adult_count || "0",
          extra_child_passengers: ticket.extra_child_count || "0",
        };
      });
      const cols = dataToDisplay?.map((a) => {
        return Object.keys(a).map((key) => {
          return {
            key,
            name: key.split("_").join(" ").toUpperCase(),
          };
        });
      });

      return {
        data: dataToDisplay,
        columns: cols[0],
      };
    } else if (item.type === "CardRecharge") {
      // const dataToDisplay = modifyData(details, "cardRecharge", item);
      const dataToDisplay = item.tickets?.map((ticket) => {
        return {
          card_number: ticket.card_number,
          subscription_number: ticket.subscription_number,
          amount: ticket.amount,
        };
      });
      const cols = dataToDisplay?.map((a) => {
        return Object.keys(a).map((key) => {
          return {
            key,
            name: key.split("_").join(" ").toUpperCase(),
          };
        });
      });

      return {
        data: dataToDisplay,
        columns: cols[0],
      };
    } else if (
      item.type === "Booking" &&
      (item.status === "deleted" || item.status === "Released")
    ) {
      if (!itemDetails || !Object.keys(itemDetails).length)
        return { data: [], columns: [] };
      const dataToDisplay = modifyData(itemDetails, "booking", item);
      console.log(dataToDisplay, "ddd");
      const cols = dataToDisplay?.map((a) => {
        return Object.keys(a).map((key) => {
          return {
            key,
            name: key.split("_").join(" ").toUpperCase(),
          };
        });
      });
      return {
        data: dataToDisplay,
        columns: cols?.[0],
      };
    } else {
      const dataToDisplay = item.tickets?.map((ticket) => {
        return {
          name: ticket.name,
          phone: ticket.phone,
          email: ticket.email,
          national_id: ticket.national_id,
          track: ticket.track,
          track_id: ticket.track_id,
          pickup_point: ticket.pickup_point,
          ticket_number: ticket.ticket_number,
          reservation_id: ticket.reservation_id,
          ticket_price: ticket.ticket_price,
          ticket_type: ticket.price_type_name,
          trip_date: ticket.trip_date,
          trip_time: ticket.trip_time,
          seat_no: ticket.seat_no,
          is_return_trip: Boolean(ticket.is_return_trip) ? "Yes" : "No",
        };
      });
      const cols = dataToDisplay?.map((a) => {
        return Object.keys(a).map((key) => {
          return {
            key,
            name: key.split("_").join(" ").toUpperCase(),
          };
        });
      });
      return {
        data: dataToDisplay,
        columns: cols[0],
      };
    }
  };

  const khbTableData = (item) => {
    const data = item.tickets?.[0]?.slaveTickets?.map((slave) => {
      return {
        name: slave.name,
        ticket_id: slave.ticket_id,
        amount: slave.amount,
        type: slave.type?.replace("-تطبيق الموبايل", ""),
      };
    });

    const columns = data?.length
      ? Object.keys(data[0]).map((key) => {
          return {
            key,
            name: key.split("_").join(" ").toUpperCase(),
          };
        })
      : [];

    return {
      data,
      columns,
    };
  };
  useEffect(() => {
    getPaidJettTransactions().then((data) => {
      if (data?.length) {
        const modifiedData = data.map((item) => {
          return {
            ...item,
            type: item.type.type,
            createdAt: transformDate(convertToJordanTime(item.createdAt)),
            refundedAt:
              item.status === "refunded"
                ? transformDate(convertToJordanTime(item.updatedAt))
                : "",
          };
        });

        setPayoneJettTransactions(modifiedData);

        const tracksD = modifiedData
          .filter((item) => item.type === "Booking")
          .map((item) => item.tickets.map((ticket) => ticket.track))
          .flat();

        if (tracksD?.length) setTracks([...new Set(tracksD)] as string[]);
      }
    });

    getReleasedJettTransactions().then((data) => {
      if (data?.length) {
        const modifiedData = data
          .map((item) => {
            return {
              ...item,
              type: "Booking",
              createdAt: transformDate(convertToJordanTime(item.createdAt)),
            };
          })
          .sort(
            (a, b) =>
              new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );

        setReleasedJettTransactions(modifiedData);
      }
    });
  }, []);

  const handleResetAll = () => {
    setFilterType([]);
    setFilterChannel([]);
    setFilterPayment([]);
    setFilterDate(undefined);
    setFilterTrack([]);
    setFilterStatus([]);
  };

  // Custom search function that includes passenger names from nested tickets
  const searchWithPassengerNames = (keySearch: string, array: any[]) => {
    if (!keySearch.length) return array;

    const normalizeText = (text: string) =>
      text
        .toLowerCase()
        .normalize("NFKD")
        .replace(/[\u064b-\u065f]/g, "");

    const searchTerm = normalizeText(keySearch);

    return array.filter((item) => {
      // Search in top-level fields
      const topLevelFields = [
        "invoice_id",
        "type",
        "channel",
        "payment",
        "status",
        "createdAt",
      ];
      const topLevelMatch = topLevelFields.some((key) => {
        const value = item[key];
        if (typeof value === "string") {
          return normalizeText(value).includes(searchTerm);
        } else if (typeof value === "number") {
          return value.toString().includes(keySearch.toString());
        }
        return false;
      });

      if (topLevelMatch) return true;

      // Search in nested ticket data for passenger names
      if (item.tickets && Array.isArray(item.tickets)) {
        const ticketMatch = item.tickets.some((ticket) => {
          // Search in ticket name, email, phone, and other relevant fields
          const ticketFields = ["name", "email", "phone", "national_id"];
          const ticketFieldMatch = ticketFields.some((field) => {
            const value = ticket[field];
            if (typeof value === "string") {
              return normalizeText(value).includes(searchTerm);
            }
            return false;
          });

          if (ticketFieldMatch) return true;

          // For KHB tickets, also search in slave tickets
          if (ticket.slaveTickets && Array.isArray(ticket.slaveTickets)) {
            return ticket.slaveTickets.some((slave) => {
              const slaveName = slave.name;
              if (typeof slaveName === "string") {
                return normalizeText(slaveName).includes(searchTerm);
              }
              return false;
            });
          }

          return false;
        });

        if (ticketMatch) return true;
      }

      return false;
    });
  };

  useEffect(() => {
    let filtered = [...payoneJettTransactions];
    if (filterType?.length) {
      filtered = filtered.filter((item) => filterType?.includes(item.type));
    }
    if (filterChannel?.length) {
      filtered = filtered.filter((item) =>
        filterChannel?.includes(item.channel)
      );
    }
    if (filterPayment?.length) {
      filtered = filtered.filter((item) =>
        filterPayment.includes(item.payment)
      );
    }
    if (filterStatus?.length) {
      filtered = filtered.filter((item) => filterStatus.includes(item.status));
    }
    if (filterDate) {
      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.createdAt);
        return (
          itemDate.getTime() >= filterDate?.from?.getTime() &&
          itemDate.getTime() <=
            (filterDate?.to?.getTime() || filterDate?.from?.getTime())
        );
      });
    }
    if (filterTrack?.length) {
      filtered = filtered.filter((item) => {
        if (item.type === "Booking") {
          return item.tickets.some((ticket) =>
            filterTrack.includes(ticket.track)
          );
        }
        return true;
      });
    }
    const data = searchWithPassengerNames(keySearch, filtered);
    setRenderedTransactions(data);

    // if date filter is applied, calculate refunded price using refundedAt date
    if (filterDate) {
      const refunded = data?.reduce((acc, item) => {
        if (item.status === "refunded") {
          const itemDate = new Date(item.updatedAt);
          if (
            itemDate.getTime() >= filterDate?.from?.getTime() &&
            itemDate.getTime() <=
              (filterDate?.to?.getTime() || filterDate?.from?.getTime())
          ) {
            return acc + item.total_price;
          }
        }
        return acc;
      }, 0);
      setRefundedPrice(refunded);
    } else {
      const refunded = data?.reduce((acc, item) => {
        if (item.status === "refunded") {
          return acc + item.total_price;
        }
        return acc;
      }, 0);
      setRefundedPrice(refunded);
    }
  }, [
    payoneJettTransactions,
    keySearch,
    filterType,
    filterChannel,
    filterPayment,
    filterDate,
    filterTrack,
    filterStatus,
  ]);

  useEffect(() => {
    let filtered = [
      ...releasedJettTransactions?.map((item) => ({
        ...item,
        status: "Released",
      })),
    ];
    const data = searchedArray(keySearchR, filtered, ["invoice_id"]);

    setRenderedReleasedTransactions(data);
  }, [keySearchR, releasedJettTransactions]);

  useEffect(() => {
    if (filterTrack?.length) {
      const totalP = renderedTransactions?.reduce((acc, item) => {
        if (item.type === "Booking") {
          return (
            acc +
            item.tickets.filter((ticket) => filterTrack.includes(ticket.track))
              .length
          );
        }
        return acc + item.totalPassengers;
      }, 0);
      setTotalPassengers(totalP);
    } else {
      const totalP = renderedTransactions?.reduce((acc, item) => {
        return acc + item.totalPassengers;
      }, 0);
      setTotalPassengers(totalP);
    }
  }, [renderedTransactions, filterTrack]);

  function convertToCSV(data) {
    const headers = Object.keys(data[0]).join(",") + "\n";
    const csvData = data
      .map((item) => {
        const values = Object.values(item).map((val) => {
          if (typeof val === "object") {
            return JSON.stringify(val);
          } else if (typeof val === "string") {
            return val + "\t";
          } else {
            return val;
          }
        });
        return values.join(",") + "\n";
      })
      .join("");

    return "\uFEFF" + headers + csvData;
  }

  const prepareDataForCSV = (item) => {
    if (item.type === "KHB") {
      const bookedDate = new Date(item.createdAt).toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "short",
        year: "numeric",
      });
      const result = [];
      result.push({
        type: item.type,
        channel: item.channel,
        ticket_type: "Master",
        name: item.tickets[0].name,
        email: item.tickets[0].email,
        booked_date: bookedDate,
        master_name: item.tickets[0].name,
        master_email: item.tickets[0].email,
        price: item.tickets[0].amount,
        total_price: item.total_price,
        ticket_id: item.tickets[0].ticket_id,
        master_ticket_id: item.tickets[0].ticket_id,
        transaction_id: item.invoice_id,
        trip_id: item.tickets[0].trip_id,
        trip_date: item.tickets[0].trip_date,
        trip_time: item.tickets[0].trip_time,
        luggage_count: item.tickets[0].luggage_count,
        extra_adult_passengers: item.tickets[0].extra_adult_count || "0",
        extra_child_passengers: item.tickets[0].extra_child_count || "0",
      });

      item.tickets[0].slaveTickets.forEach((ticket) => {
        result.push({
          type: item.type,
          channel: item.channel,
          ticket_type: ticket.type?.replace("-تطبيق الموبايل", ""),
          name: ticket.name,
          email: ticket.email,
          booked_date: bookedDate,
          master_name: item.tickets[0].name,
          master_email: item.tickets[0].email,
          price: ticket.amount,
          total_price: 0,
          ticket_id: ticket.ticket_id,
          master_ticket_id: ticket.master_ticket_id,
          transaction_id: item.invoice_id,
          trip_id: ticket.trip_id,
          trip_date: ticket.trip_date,
          trip_time: ticket.trip_time,
          luggage_count: 0,
          extra_adult_passengers: "0",
          extra_child_passengers: "0",
        });
      });
      return result;
    } else if (item.type === "CardRecharge") {
      return item.tickets.map((ticket) => ({
        type: item.type,
        channel: item.channel,
        card_number: ticket.card_number,
        subscription_number: ticket.subscription_number,
        amount: ticket.amount,
        transaction_id: item.invoice_id,
      }));
    } else {
      const bookedDate = item.createdAt;

      // convert booked date to jordan time zone and format it
      // const bookedDate = new Date(item.createdAt).toLocaleDateString("en-GB", {
      //   day: "2-digit",
      //   month: "short",
      //   year: "numeric",
      // });

      const refundedDate =
        item.status === "refunded" ? transformDate(item.updatedAt) : "";
      return item.tickets.map((ticket) => ({
        type: item.type,
        channel: item.channel,
        name: ticket.name
          .replace(/,/g, " ")
          .replace(/\s+/g, " ")
          .trim()
          .replace(/\n/g, " "),
        trip_name: ticket.track,
        track_id: ticket.track_id,
        pickup_point: ticket.pickup_point,
        tel: ticket.phone,
        email: ticket.email,
        national_id: ticket.national_id,
        reservation_id: ticket.reservation_id,
        transaction_id: item.invoice_id,
        ticket_price: ticket.ticket_price,
        ticket_type: ticket.price_type_name,
        booked_date: bookedDate,
        refunded_date: refundedDate,
        trip_date: ticket.trip_date,
        trip_time: ticket.trip_time,
        trip_seat: ticket.seat_no,
        payment_way: item.payment,
        status: item.status,
        is_return_trip: Boolean(ticket.is_return_trip) ? "Yes" : "No",
      }));
    }
  };

  const exportToCSV2 = (items) => {
    const csvDataArrays = {
      KHB: [],
      CardRecharge: [],
      Booking: [],
    };

    const filteredData = renderedTransactions;

    filteredData.forEach((item) => {
      const preparedData = prepareDataForCSV(item);
      if (item.type === "KHB") {
        csvDataArrays.KHB.push(...preparedData);
      } else if (item.type === "CardRecharge") {
        csvDataArrays.CardRecharge.push(...preparedData);
      } else {
        csvDataArrays.Booking.push(...preparedData);
      }
    });

    const csvFiles = {};
    Object.entries(csvDataArrays).forEach(([type, data]) => {
      if (data.length > 0) {
        const csvContent = convertToCSV(data);

        csvFiles[type] = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
      }
    });
    for (const type in csvFiles) {
      const link = document.createElement("a");
      if (link.download !== undefined) {
        const url = URL.createObjectURL(csvFiles[type]);
        link.setAttribute("href", url);
        link.setAttribute("download", `${type}_data.csv`);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };

  return (
    <>
      {Boolean(item) ? (
        <FullModal
          className="!w-5/6"
          title="Details"
          isOpen={Boolean(item)}
          onClose={() => setItem(null)}
        >
          <MainTable {...tableData()} />
          {item?.type === "KHB" ? (
            <div className="mt-3">
              <MainTable {...khbTableData(item)} />
            </div>
          ) : null}
        </FullModal>
      ) : null}

      <FullModal
        key={"exportModal"}
        title="Export Data"
        isOpen={exportModel}
        onClose={() => setExportModel(false)}
        className="flex flex-col gap-3"
      >
        <h1>Export Filtered Transactions</h1>
        {!renderedTransactions?.length && (
          <p className="text-red-500">No transactions to export</p>
        )}
        <Button
          disabled={!renderedTransactions?.length}
          loading={loading}
          onClick={exportToCSV2}
          className="mt-3"
        >
          Export
        </Button>
      </FullModal>

      {payoneJettTransactions?.length > 0 ? (
        <div className="grid grid-cols-4 gap-3">
          <div className="col-span-1 mt-4">
            <h1>Jett Chatbot Invoices</h1>
            <small>
              Total: {payoneJettTransactions?.length || 0} invoices | Viewing:{" "}
              {renderedTransactions?.length || 0} invoices
            </small>
            <hr />
            <small>
              {/* exclude refunded */}
              Price Paid:{" "}
              {renderedTransactions
                ?.reduce((acc, item) => {
                  if (item.status === "refunded") return acc;
                  return acc + item.total_price;
                }, 0)
                .toFixed(2)}{" "}
              | Refunded Price: {refundedPrice?.toFixed(2)}
              <br />
              Total :{" "}
              {renderedTransactions
                ?.reduce((acc, item) => {
                  return acc + item.total_price;
                }, 0)
                .toFixed(2)}
              <hr />
              Total Passengers : {totalPassengers}
            </small>
            <div className="space-y-4 sticky top-10 h-fit border border-white/25 p-2 rounded-md mt-4">
              <button
                type="button"
                className="text-sm whitespace-nowrap underline underline-offset-4 float-right"
                onClick={handleResetAll}
              >
                Reset All
              </button>
              <p className="block text-xs font-medium ">Filters</p>

              <div className="mt-1 space-y-2">
                <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
                  <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
                    <span className="text-sm font-medium"> Type </span>

                    <span className="transition group-open:-rotate-180">
                      <ChevronDown className="h-4 w-4" />
                    </span>
                  </summary>

                  <div className="border-t border-gray-200">
                    <header className="flex items-center justify-end p-4">
                      <button
                        type="button"
                        className="text-sm  underline underline-offset-4"
                        onClick={() => setFilterType([])}
                      >
                        Reset
                      </button>
                    </header>

                    <ul className="space-y-1 border-t border-gray-200 p-4">
                      <li>
                        <label
                          htmlFor="filterTypeBooking"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="typeFilter"
                            type="checkbox"
                            id="filterTypeBooking"
                            className="text-primary"
                            checked={filterType.includes("Booking")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterType([...filterType, "Booking"]);
                              } else {
                                setFilterType(
                                  filterType.filter(
                                    (item) => item !== "Booking"
                                  )
                                );
                                setFilterTrack([]);
                              }
                            }}
                          />

                          <span className="text-sm font-medium ">Booking</span>
                        </label>
                      </li>

                      <li>
                        <label
                          htmlFor="filterTypeKHB"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="typeFilter"
                            type="checkbox"
                            id="filterTypeKHB"
                            className="text-primary"
                            checked={filterType.includes("KHB")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterType([...filterType, "KHB"]);
                              } else {
                                setFilterType(
                                  filterType.filter((item) => item !== "KHB")
                                );
                              }
                            }}
                          />

                          <span className="text-sm font-medium ">KHB</span>
                        </label>
                      </li>

                      <li>
                        <label
                          htmlFor="filterTypeCardRecharge"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="typeFilter"
                            type="checkbox"
                            id="filterTypeCardRecharge"
                            className="text-primary"
                            checked={filterType.includes("CardRecharge")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterType([...filterType, "CardRecharge"]);
                              } else {
                                setFilterType(
                                  filterType.filter(
                                    (item) => item !== "CardRecharge"
                                  )
                                );
                              }
                            }}
                          />
                          <span className="text-sm font-medium ">
                            JETT Card Recharge
                          </span>
                        </label>
                      </li>
                    </ul>
                  </div>
                </details>
                {filterType?.includes("Booking") ? (
                  <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
                    {/* use tracks array to chhose from to filter */}
                    <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
                      <span className="text-sm font-medium"> Track </span>

                      <span className="transition group-open:-rotate-180">
                        <ChevronDown className="h-4 w-4" />
                      </span>
                    </summary>
                    <div className="border-t border-gray-200">
                      <header className="flex items-center justify-end p-4">
                        <button
                          type="button"
                          className="text-sm  underline underline-offset-4"
                          onClick={() => setFilterTrack([])}
                        >
                          Reset
                        </button>
                      </header>
                      <ul className="space-y-1 border-t border-gray-200 p-4 h-[300px] overflow-y-auto">
                        {tracks?.map((track) => (
                          <li key={track}>
                            <label
                              htmlFor={`filterTrack${track}`}
                              className="inline-flex items-center gap-2"
                            >
                              <input
                                name="trackFilter"
                                type="checkbox"
                                id={`filterTrack${track}`}
                                className="text-primary"
                                checked={filterTrack.includes(track)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setFilterTrack([...filterTrack, track]);
                                  } else {
                                    setFilterTrack(
                                      filterTrack.filter(
                                        (item) => item !== track
                                      )
                                    );
                                  }
                                }}
                              />
                              <span className="text-sm font-medium ">
                                {track}
                              </span>
                            </label>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </details>
                ) : null}
                <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
                  <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
                    <span className="text-sm font-medium"> Channel </span>

                    <span className="transition group-open:-rotate-180">
                      <ChevronDown className="h-4 w-4" />
                    </span>
                  </summary>

                  <div className="border-t border-gray-200">
                    <header className="flex items-center justify-end p-4">
                      <button
                        type="button"
                        className="text-sm  underline underline-offset-4"
                        onClick={() => setFilterChannel([])}
                      >
                        Reset
                      </button>
                    </header>

                    <ul className="space-y-1 border-t border-gray-200 p-4">
                      <li>
                        <label
                          htmlFor="filterChannelWeb"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="channelFilter"
                            type="checkbox"
                            id="filterChannelWeb"
                            className="text-primary"
                            checked={filterChannel?.includes("web")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterChannel([...filterChannel, "web"]);
                              } else {
                                setFilterChannel(
                                  filterChannel.filter((item) => item !== "web")
                                );
                              }
                            }}
                          />

                          <span className="text-sm font-medium ">Web</span>
                        </label>
                      </li>

                      <li>
                        <label
                          htmlFor="filterChannelWhatsApp"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="channelFilter"
                            type="checkbox"
                            id="filterChannelWhatsApp"
                            className="text-primary"
                            checked={filterChannel?.includes("whatsapp")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterChannel([
                                  ...filterChannel,
                                  "whatsapp",
                                ]);
                              } else {
                                setFilterChannel(
                                  filterChannel.filter(
                                    (item) => item !== "whatsapp"
                                  )
                                );
                              }
                            }}
                          />

                          <span className="text-sm font-medium ">WhatsApp</span>
                        </label>
                      </li>

                      <li>
                        <label
                          htmlFor="filterChannelFacebook"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="channelFilter"
                            type="checkbox"
                            id="filterChannelFacebook"
                            className="text-primary"
                            checked={filterChannel?.includes("facebook")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterChannel([
                                  ...filterChannel,
                                  "facebook",
                                ]);
                              } else {
                                setFilterChannel(
                                  filterChannel.filter(
                                    (item) => item !== "facebook"
                                  )
                                );
                              }
                            }}
                          />
                          <span className="text-sm font-medium ">Facebook</span>
                        </label>
                      </li>
                      <li>
                        <label
                          htmlFor="filterChannelInstagram"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="channelFilter"
                            type="checkbox"
                            id="filterChannelInstagram"
                            className="text-primary"
                            checked={filterChannel?.includes("instagram")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterChannel([
                                  ...filterChannel,
                                  "instagram",
                                ]);
                              } else {
                                setFilterChannel(
                                  filterChannel.filter(
                                    (item) => item !== "instagram"
                                  )
                                );
                              }
                            }}
                          />
                          <span className="text-sm font-medium ">
                            Instagram
                          </span>
                        </label>
                      </li>
                    </ul>
                  </div>
                </details>
                <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
                  <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
                    <span className="text-sm font-medium"> Payment </span>

                    <span className="transition group-open:-rotate-180">
                      <ChevronDown className="h-4 w-4" />
                    </span>
                  </summary>

                  <div className="border-t border-gray-200">
                    <header className="flex items-center justify-end p-4">
                      <button
                        type="button"
                        className="text-sm  underline underline-offset-4"
                        onClick={() => {
                          setFilterPayment([]);

                          setFilterStatus([]);
                        }}
                      >
                        Reset
                      </button>
                    </header>

                    <ul className="space-y-1 border-t border-gray-200 p-4">
                      <li>
                        <label
                          htmlFor="filterPaymentPayone"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="paymentFilter"
                            type="checkbox"
                            id="filterPaymentPayone"
                            className="text-primary"
                            checked={filterPayment.includes("payone")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterPayment([...filterPayment, "payone"]);
                              } else {
                                setFilterPayment(
                                  filterPayment.filter(
                                    (item) => item !== "payone"
                                  )
                                );
                              }
                            }}
                          />

                          <span className="text-sm font-medium ">PayOne</span>
                        </label>
                      </li>

                      <li>
                        <label
                          htmlFor="filterPaymentJettCard"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="paymentFilter"
                            type="checkbox"
                            id="filterPaymentJettCard"
                            className="text-primary"
                            checked={filterPayment?.includes("jett_card")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterPayment([
                                  ...filterPayment,
                                  "jett_card",
                                ]);
                              } else {
                                setFilterPayment(
                                  filterPayment.filter(
                                    (item) => item !== "jett_card"
                                  )
                                );
                              }
                            }}
                          />

                          <span className="text-sm font-medium ">
                            JETT Card
                          </span>
                        </label>
                      </li>

                      <li>
                        <label
                          htmlFor="filterPaymentPaid"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="paymentFilter"
                            type="checkbox"
                            id="filterPaymentPaid"
                            className="text-primary"
                            checked={filterStatus.includes("paid")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterStatus([...filterStatus, "paid"]);
                              } else {
                                setFilterStatus(
                                  filterStatus.filter((item) => item !== "paid")
                                );
                              }
                            }}
                          />
                          <span className="text-sm font-medium ">Paid</span>
                        </label>
                      </li>
                      <li>
                        <label
                          htmlFor="filterPaymentincomplete"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="paymentFilter"
                            type="checkbox"
                            id="filterPaymentincomplete"
                            className="text-primary"
                            checked={filterStatus.includes("incomplete")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterStatus([
                                  ...filterStatus,
                                  "incomplete",
                                ]);
                              } else {
                                setFilterStatus(
                                  filterStatus.filter(
                                    (item) => item !== "incomplete"
                                  )
                                );
                              }
                            }}
                          />
                          <span className="text-sm font-medium ">
                            Incomplete
                          </span>
                        </label>
                      </li>

                      <li>
                        <label
                          htmlFor="filterPaymentRefunded"
                          className="inline-flex items-center gap-2"
                        >
                          <input
                            name="paymentFilter"
                            type="checkbox"
                            id="filterStatusRefunded"
                            className="text-primary"
                            checked={filterStatus.includes("refunded")}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setFilterStatus([...filterStatus, "refunded"]);
                              } else {
                                setFilterStatus(
                                  filterStatus.filter(
                                    (item) => item !== "refunded"
                                  )
                                );
                              }
                            }}
                          />
                          <span className="text-sm font-medium ">Refunded</span>
                        </label>
                      </li>
                    </ul>
                  </div>
                </details>
                <details className="group overflow-hidden rounded border border-gray-300 [&_summary::-webkit-details-marker]:hidden">
                  <summary className="flex cursor-pointer items-center justify-between gap-2 p-4  transition">
                    <span className="text-sm font-medium"> Date </span>

                    <span className="transition group-open:-rotate-180">
                      <ChevronDown className="h-4 w-4" />
                    </span>
                  </summary>
                  <div className="border-t border-gray-200">
                    <header className="flex items-center justify-end p-4">
                      <button
                        type="button"
                        className="text-sm  underline underline-offset-4"
                        onClick={() => setFilterDate(undefined)}
                      >
                        Reset
                      </button>
                    </header>
                    <div className="p-4 w-fit">
                      <DatePickerWithRange
                        className="!w-[210px]"
                        date={filterDate}
                        setDate={setFilterDate}
                      />
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
          <div className="pt-5 space-y-3 col-span-3">
            <div className="flex gap-5">
              <CustomSearch
                placeholder="Find an invoice or passenger name..."
                onChange={(value) => setKeySearch(value)}
              />
              <Button onClick={() => setExportModel(true)}>
                Export Data To CSV
              </Button>
            </div>
            <MainTable
              data={renderedTransactions}
              columns={[
                {
                  key: "invoice_id",
                  name: "Invoice",
                },
                {
                  key: "type",
                  name: "Type",
                },
                {
                  key: "channel",
                  name: "Channel",
                },
                {
                  key: "status",
                  name: "Status",
                },
                {
                  key: "total_price",
                  name: "Total Price",
                },
                {
                  key: "createdAt",
                  name: "Date",
                },
                {
                  key: "refundedAt",
                  name: "Refunded At",
                },
              ]}
              actions={[
                {
                  label: "View",
                  onClick: (item) => {
                    setItem(item);
                  },
                  icon: View,
                },
                {
                  label: "Show Conversation",
                  onClick: (item) => {
                    const { conversation_id } = item;
                    // copy conversation id
                    navigator.clipboard.writeText(conversation_id);
                    toast("conversation ID copied");
                  },
                  icon: Copy,
                },
              ]}
              itemsPerPage={10}
            />
          </div>
        </div>
      ) : null}

      {releasedJettTransactions?.length > 0 ? (
        <div className="pt-5 space-y-3 col-span-3">
          <p>Released Tickets</p>
          <div className="flex gap-5">
            <CustomSearch
              placeholder="Find an invoice..."
              onChange={(value) => setKeySearchR(value)}
            />
          </div>
          <MainTable
            data={renderedReleasedTransactions}
            columns={[
              {
                key: "invoice_id",
                name: "Invoice",
              },
              {
                key: "type",
                name: "Type",
              },
              {
                key: "channel",
                name: "Channel",
              },
              {
                key: "payment",
                name: "Payment",
              },
              {
                key: "status",
                name: "Status",
              },
              {
                key: "total_price",
                name: "Total Price",
              },
              {
                key: "createdAt",
                name: "Date",
              },
            ]}
            actions={[
              {
                label: "View",
                onClick: (item) => {
                  setItem(item);
                },
                icon: View,
              },
            ]}
            itemsPerPage={10}
          />
        </div>
      ) : null}
    </>
  );
}
