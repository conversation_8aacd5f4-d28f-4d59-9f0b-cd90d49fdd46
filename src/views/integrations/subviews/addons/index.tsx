import { SubPageHeader } from "common/components/headers";
import React from "react";
import {
  <PERSON>t<PERSON><PERSON><PERSON>,
  LeadsAddon,
  ReportsAddon,
  VoiceAddon,
  WeatherAddon,
} from "./components";
import SentimentAddon from "./components/sentiment/sentiment.addon";
import { InternalLivechatAddon } from "./components/internalLivechat/intenalLivechat.addon";
import useUserStore from "store/user/user.store";
import { TicketingSystemAddon } from "./components/ticketingSystem/ticketingSystem.addon";
import useBotStore from "store/bot/bot.store";

export const AddonsView = () => {
const user_id = useUserStore((state) => state.user.user_id);
const bot_id = useBotStore((state) => state.bot.bot_id);
  return (
    <div className="space-y-3">
      <SubPageHeader
        title="AddOns"
        description="Add features to your chatbot!"
      />
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3  gap-4">
        <CartAddon />
        <VoiceAddon />
        {/* <WeatherAddon /> */}
        <ReportsAddon />
        <LeadsAddon />
        {/* <SentimentAddon /> */}
        {/* {user_id === 17 ? 
        <> */}
        <InternalLivechatAddon /> 

        {
          user_id !== 324 && bot_id !== 920 && bot_id !== 926 ?
        
        <TicketingSystemAddon/> : null
        }
        {/* </>
        : null} */}
      </div>
    </div>
  );
};
